from __future__ import annotations

import logging
import json
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, Query, Head<PERSON>
from typing import Optional, List, TYPE_CHECKING
from loguru import logger

if TYPE_CHECKING:
    from nebula.services.x_ray.xray_service import XRayService

from nebula.services.x_ray.types import XRayTypeFilter, XRaySortBy, XRayUpdateData
from nebula.web.api.dependencies import get_xray_service
from nebula.web.api.x_ray.schema import (
    XRayCreateStep1Body,
    XRayCreateStep2Response,
    XRayResponse,
    XRayTemplateResponse,
    XRayListResponse,
    XRayTemplateListResponse,
    XRayUpdateRequestBody,
    XRayCreateStep1Response,
    XRayCreateStep2Body,
    XRayCreateStep3Body,
    XRayNotificationResponse,
    MarkNotificationsSeenResponse,
    xray_model_from_db_model,
    xray_template_model_from_db_model,
)
from nebula.web.api.auth import user_id_from_header_or_err

router = APIRouter(tags=["x-ray"])


@router.post("/create/step1", response_model=XRayCreateStep1Response)
async def create_xray_step1(
    request: XRayCreateStep1Body,
    _: str = Depends(user_id_from_header_or_err),
    service: "XRayService" = Depends(get_xray_service),
):
    """
    Step 1 of X-Ray creation: Generate X-Ray type and prompt from description
    """
    try:
        result = await service.generate_step1_type_and_prompt(request.description)
        return XRayCreateStep1Response(
            success=True,
            message="Success",
            data=XRayCreateStep1Response.Data(
                xrayType=result.xray_type,
                prompt=result.prompt,
            ),
        )
    except ValueError as e:
        logging.error(f"failed to generate xray creation step 1 outputs: {e}")
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        logging.error(f"failed to generate xray creation step 1 outputs: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/create/step2", response_model=XRayCreateStep2Response)
async def create_xray_step2(
    request: XRayCreateStep2Body,
    _: str = Depends(user_id_from_header_or_err),
    service: "XRayService" = Depends(get_xray_service),
):
    """
    Step 2 of X-Ray creation: Generate title, emoji, and short summary from X-Ray type and prompt
    """
    try:
        result = await service.generate_step2_metadata(
            xray_type=request.xrayType,
            prompt=request.prompt,
        )
        return XRayCreateStep2Response(
            success=True,
            message="Success",
            data=XRayCreateStep2Response.Data(
                title=result.title,
                emoji=result.emoji,
                shortSummary=result.short_summary,
            ),
        )
    except ValueError as e:
        raise HTTPException(status_code=422, detail=str(e))
    except Exception:
        raise HTTPException(status_code=500, detail="Internal server error")


@router.post("/create/step3", response_model=XRayResponse)
async def create_xray_step3(
    request: XRayCreateStep3Body,
    user_id: str = Depends(user_id_from_header_or_err),
    service: "XRayService" = Depends(get_xray_service),
):
    """
    Step 3 of X-Ray creation: Create X-Ray record with all generated data
    """
    try:
        xray = await service.create_xray(
            user_id=int(user_id),
            description=request.description,
            xray_type=request.xrayType,
            prompt=request.prompt,
            title=request.title,
            emoji=request.emoji,
            short_summary=request.shortSummary,
            timezone=request.timezone,
            cron_expression=request.frequency,
            alert_channels=request.alertChannels,
        )

        return XRayResponse(
            success=True,
            message="Success",
            data=XRayResponse.Data(xray=xray_model_from_db_model(xray, None)),
        )
    except ValueError as e:
        raise HTTPException(status_code=422, detail=str(e))
    except Exception:
        logging.error("Error creating X-Ray", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/", response_model=XRayListResponse)
async def list_xrays(
    user_id: str = Depends(user_id_from_header_or_err),
    service: "XRayService" = Depends(get_xray_service),
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0),
    type_filter: Optional[XRayTypeFilter] = None,
    sort_by: XRaySortBy = XRaySortBy.LAST_UPDATED,
):
    """
    List X-Rays with pagination, filtering, and sorting
    """
    xrays, total, has_more = await service.get_xrays_paginated(
        user_id=int(user_id),
        limit=limit,
        skip=offset,
        xray_type=type_filter,
        sort_by=sort_by,
    )

    return XRayListResponse(
        success=True,
        message="Success",
        data=XRayListResponse.Data(
            xrays=[
                XRayResponse.XRay.model_validate(xray, from_attributes=True)
                for xray in xrays
            ],
            total=total,
            hasMore=has_more,
        ),
    )


@router.get("/templates", response_model=XRayTemplateListResponse)
async def list_xray_templates(
    user_id: str = Depends(user_id_from_header_or_err),
    service: "XRayService" = Depends(get_xray_service),
    limit: int = Query(10, ge=1, le=100),
    offset: int = Query(0, ge=0),
):
    """
    List X-Ray templates with pagination (Rumi-generated templates only)
    """
    templates, total, has_more = await service.get_xray_templates_paginated(
        limit=limit,
        skip=offset,
    )

    return XRayTemplateListResponse(
        success=True,
        message="Success",
        data=XRayTemplateListResponse.Data(
            templates=[
                xray_template_model_from_db_model(template) for template in templates
            ],
            total=total,
            hasMore=has_more,
        ),
    )


@router.get("/xray-templates/{template_id}", response_model=XRayTemplateResponse)
async def get_xray_template(
    template_id: int,
    _: str = Depends(user_id_from_header_or_err),
    service: "XRayService" = Depends(get_xray_service),
):
    """
    Get an X-Ray template by ID (Rumi-generated templates only)
    """
    try:
        template = await service.get_xray_template_by_id(template_id=template_id)

        if not template:
            raise HTTPException(status_code=404, detail="Template not found")

        return XRayTemplateResponse(
            success=True,
            message="Success",
            data=XRayTemplateResponse.Data(
                template=xray_template_model_from_db_model(template)
            ),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting X-Ray template: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.patch("/{xray_id}", response_model=XRayResponse)
async def update_xray(
    xray_id: int,
    request: XRayUpdateRequestBody,
    user_id: str = Header(..., alias="x-user-id"),
    service: "XRayService" = Depends(get_xray_service),
):
    """Update an X-Ray"""
    try:
        payload = XRayUpdateData()

        if request.title is not None:
            payload.title = request.title
        if request.alertChannels is not None:
            payload.alert_channels = request.alertChannels
        if request.isActive is not None:
            payload.is_active = request.isActive
        if request.frequency is not None:
            payload.frequency = request.frequency
        if request.timezone is not None:
            payload.timezone = request.timezone

        updated_xray = await service.update_xray(
            xray_id=xray_id,
            user_id=int(user_id),
            update_data=payload,
        )

        if not updated_xray:
            raise HTTPException(status_code=404, detail="X-Ray not found")

        # Get the updated X-Ray with current commit (same pattern as get_xray)
        xray_with_commit = await service.get_xray_with_current_commit(xray_id=xray_id)
        if not xray_with_commit:
            raise HTTPException(status_code=404, detail="X-Ray not found")

        return XRayResponse(
            success=True,
            message="Success",
            data=XRayResponse.Data(
                xray=xray_model_from_db_model(xray_with_commit[0], xray_with_commit[1])
            ),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating X-Ray: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{xray_id}", response_model=XRayResponse)
async def get_xray(
    xray_id: int,
    user_id: str = Depends(user_id_from_header_or_err),
    service: "XRayService" = Depends(get_xray_service),
):
    """
    Get a single X-Ray by ID
    """

    xray_with_commit_iter = await service.get_xray_with_current_commit(xray_id=xray_id)
    if not xray_with_commit_iter:
        raise HTTPException(status_code=404, detail="X-Ray not found")

    xray = xray_with_commit_iter[0]
    commit = xray_with_commit_iter[1]
    if xray.owner_id != int(user_id):
        raise HTTPException(status_code=403, detail="Forbidden")

    return XRayResponse(
        success=True,
        message="Success",
        data=XRayResponse.Data(xray=xray_model_from_db_model(xray, commit)),
    )


@router.post("/{xray_id}/share", response_model=XRayTemplateResponse)
async def share_xray_as_template(
    xray_id: int,
    user_id: str = Depends(user_id_from_header_or_err),
    service: "XRayService" = Depends(get_xray_service),
):
    """
    Share an X-Ray as a template. Creates a template from the X-Ray's settings.
    Returns existing template if already shared (idempotent behavior).
    """
    try:
        template = await service.share_xray_as_template(
            xray_id=xray_id, user_id=int(user_id)
        )

        if not template:
            raise HTTPException(status_code=404, detail="X-Ray not found")

        return XRayTemplateResponse(
            success=True,
            message="Success",
            data=XRayTemplateResponse.Data(
                template=xray_template_model_from_db_model(template)
            ),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error sharing X-Ray as template: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.delete("/{xray_id}")
async def delete_xray(
    xray_id: int,
    user_id: str = Depends(user_id_from_header_or_err),
    service: "XRayService" = Depends(get_xray_service),
):
    """
    Delete an X-Ray by ID
    """
    try:
        deleted = await service.delete_xray(xray_id=xray_id, user_id=int(user_id))

        if not deleted:
            raise HTTPException(status_code=404, detail="X-Ray not found")

        return {
            "success": True,
            "message": "X-Ray deleted successfully",
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting X-Ray: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.get("/{xray_id}/notifications", response_model=XRayNotificationResponse)
async def get_xray_notifications(
    xray_id: int,
    user_id: str = Depends(user_id_from_header_or_err),
    service: "XRayService" = Depends(get_xray_service),
    limit: int = Query(30, ge=1, le=100),
    offset: int = Query(0, ge=0),
):
    """
    Get notifications for a specific X-Ray with pagination
    """
    try:
        notifications, total, has_more = await service.get_xray_notifications_paginated(
            xray_id=xray_id,
            user_id=int(user_id),
            limit=limit,
            offset=offset,
        )

        notification_responses: List[XRayNotificationResponse.Notification] = []
        for notification in notifications:
            notification_response = XRayNotificationResponse.Notification(
                id=notification["id"],
                xray_doc_commit_id=notification["xray_doc_commit_id"],
                user_id=notification["user_id"],
                title=notification["title"],
                seen=notification["seen"],
                content=notification["content"],
                source=json.loads(notification["source"])
                if notification.get("source")
                else {},
                created_at=int(notification["created_at"].timestamp()),
                updated_at=int(notification["updated_at"].timestamp()),
            )
            notification_responses.append(notification_response)

        return XRayNotificationResponse(
            success=True,
            message="Success",
            data=XRayNotificationResponse.Data(
                notifications=notification_responses,
                total=total,
                hasMore=has_more,
            ),
        )
    except Exception as e:
        logger.error(f"Error getting X-Ray notifications: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")


@router.patch(
    "/{xray_id}/notifications/mark-seen", response_model=MarkNotificationsSeenResponse
)
async def mark_xray_notifications_seen(
    xray_id: int,
    user_id: str = Depends(user_id_from_header_or_err),
    service: "XRayService" = Depends(get_xray_service),
):
    """
    Mark all unseen notifications for an X-Ray as seen
    """
    try:
        marked_count = await service.mark_xray_notifications_seen(
            xray_id=xray_id,
            user_id=int(user_id),
        )

        return MarkNotificationsSeenResponse(
            success=True,
            message="Success",
            data=MarkNotificationsSeenResponse.Data(markedCount=marked_count),
        )
    except Exception as e:
        logger.error(f"Error marking X-Ray notifications as seen: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")
